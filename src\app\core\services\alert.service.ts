import { Injectable } from '@angular/core';
import { LocalNotifications } from '@capacitor/local-notifications';
import { BehaviorSubject, Observable } from 'rxjs';
import { AlertController } from '@ionic/angular';
import { Alerta, Radar, Localizacao } from '../models';

@Injectable({
    providedIn: 'root'
})
export class AlertService {
    private alertasSubject = new BehaviorSubject<Alerta[]>([]);
    public alertas$ = this.alertasSubject.asObservable();

    private alertasAtivos: Map<string, Alerta> = new Map();
    private proximidadeAlerta = 1000; // metros
    private velocidadeAlerta = 5; // km/h acima do limite

    constructor(private alertController: AlertController) {
        this.inicializarNotificacoes();
    }

    private async inicializarNotificacoes(): Promise<void> {
        try {
            await LocalNotifications.requestPermissions();
        } catch (error) {
            console.error('Erro ao solicitar permissões de notificação:', error);
        }
    }

    verificarProximidadeRadar(localizacao: Localizacao, radares: Radar[]): void {
        radares.forEach(radar => {
            const distancia = this.calcularDistancia(
                localizacao.latitude,
                localizacao.longitude,
                radar.latitude,
                radar.longitude
            );

            const alertaId = `radar_${radar.id}`;

            if (distancia <= this.proximidadeAlerta && !this.alertasAtivos.has(alertaId)) {
                const alerta: Alerta = {
                    id: alertaId,
                    tipo: 'radar',
                    mensagem: `Radar ${radar.tipo} em ${Math.round(distancia)}m - Limite: ${radar.velocidadeMaxima}km/h`,
                    radar: radar,
                    distancia: distancia,
                    ativo: true
                };

                this.criarAlerta(alerta);
            } else if (distancia > this.proximidadeAlerta && this.alertasAtivos.has(alertaId)) {
                this.removerAlerta(alertaId);
            }
        });
    }

    verificarExcessoVelocidade(localizacao: Localizacao, radar: Radar): void {
        const velocidadeAtual = localizacao.velocidade * 3.6; // m/s para km/h
        const alertaId = `velocidade_${radar.id}`;

        if (velocidadeAtual > (radar.velocidadeMaxima + this.velocidadeAlerta)) {
            if (!this.alertasAtivos.has(alertaId)) {
                const alerta: Alerta = {
                    id: alertaId,
                    tipo: 'velocidade',
                    mensagem: `Velocidade: ${Math.round(velocidadeAtual)}km/h - Limite: ${radar.velocidadeMaxima}km/h`,
                    radar: radar,
                    ativo: true
                };

                this.criarAlerta(alerta);
            }
        } else if (this.alertasAtivos.has(alertaId)) {
            this.removerAlerta(alertaId);
        }
    }

    private criarAlerta(alerta: Alerta): void {
        this.alertasAtivos.set(alerta.id, alerta);

        const alertas = this.alertasSubject.value;
        alertas.push(alerta);
        this.alertasSubject.next([...alertas]);

        this.enviarNotificacao(alerta);
    }

    private removerAlerta(alertaId: string): void {
        this.alertasAtivos.delete(alertaId);

        const alertas = this.alertasSubject.value.filter(a => a.id !== alertaId);
        this.alertasSubject.next(alertas);
    }

    private async enviarNotificacao(alerta: Alerta): Promise<void> {
        try {
            await LocalNotifications.schedule({
                notifications: [{
                    title: alerta.tipo === 'radar' ? 'Radar Detectado' : 'Excesso de Velocidade',
                    body: alerta.mensagem,
                    id: Date.now(),
                    schedule: { at: new Date(Date.now() + 1000) }
                }]
            });
        } catch (error) {
            console.error('Erro ao enviar notificação:', error);
        }
    }

    private calcularDistancia(lat1: number, lng1: number, lat2: number, lng2: number): number {
        const R = 6371000; // Raio da Terra em metros
        const dLat = this.toRadians(lat2 - lat1);
        const dLng = this.toRadians(lng2 - lng1);
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    private toRadians(degrees: number): number {
        return degrees * (Math.PI / 180);
    }

    configurarDistanciaAlerta(distancia: number): void {
        this.proximidadeAlerta = distancia;
    }

    configurarLimiteVelocidade(limite: number): void {
        this.velocidadeAlerta = limite;
    }

    /**
     * Exibe um alerta simples para o usuário
     */
    async showAlert(header: string, message: string, buttons: string[] = ['OK']): Promise<void> {
        const alert = await this.alertController.create({
            header,
            message,
            buttons
        });

        await alert.present();
    }

    /**
     * Exibe um alerta de confirmação e retorna a resposta do usuário
     */
    async showConfirm(header: string, message: string, confirmText: string = 'Sim', cancelText: string = 'Não'): Promise<boolean> {
        return new Promise(async (resolve) => {
            const alert = await this.alertController.create({
                header,
                message,
                buttons: [
                    {
                        text: cancelText,
                        role: 'cancel',
                        handler: () => resolve(false)
                    },
                    {
                        text: confirmText,
                        handler: () => resolve(true)
                    }
                ]
            });

            await alert.present();
        });
    }

    /**
     * Cria um alerta personalizado (usado pelo componente do mapa)
     */
    createAlert(tipo: 'radar' | 'velocidade', mensagem: string, radar?: Radar, distancia?: number): void {
        const alertaId = `${tipo}_${radar?.id || Date.now()}`;

        const alerta: Alerta = {
            id: alertaId,
            tipo,
            mensagem,
            radar,
            distancia,
            ativo: true
        };

        this.criarAlerta(alerta);
    }
}

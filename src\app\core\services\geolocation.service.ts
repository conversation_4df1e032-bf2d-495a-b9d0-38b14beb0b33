import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval, map, switchMap, catchError, of, filter } from 'rxjs';
import { Geolocation, Position } from '@capacitor/geolocation';
import { Capacitor } from '@capacitor/core';
import { Localizacao } from '../models/radar.model';
import { GeolocationConfig, DEFAULT_GEOLOCATION_CONFIG, INDOOR_GEOLOCATION_CONFIG, FAST_GEOLOCATION_CONFIG } from '../config/geolocation.config';

@Injectable({
    providedIn: 'root'
})
export class GeolocationService {
    private currentLocationSubject = new BehaviorSubject<Localizacao | null>(null);
    public currentLocation$ = this.currentLocationSubject.asObservable();

    private watchId: string | number | null = null;
    private isTracking = false;
    private isNative = Capacitor.isNativePlatform();
    private config: GeolocationConfig = DEFAULT_GEOLOCATION_CONFIG;

    constructor() { }

    /**
     * Configura as configurações de geolocalização
     */
    setConfig(config: Partial<GeolocationConfig>): void {
        this.config = { ...DEFAULT_GEOLOCATION_CONFIG, ...config };
        console.log('Configuração de geolocalização atualizada:', this.config);
    }

    /**
     * Detecta automaticamente e aplica a melhor configuração baseada no ambiente
     */
    async autoConfigureForEnvironment(): Promise<void> {
        try {
            // Detectar se estamos em ambiente indoor/com GPS fraco
            const isIndoorEnvironment = await this.detectIndoorEnvironment();

            if (isIndoorEnvironment) {
                console.log('Ambiente indoor detectado, aplicando configuração otimizada');
                this.setConfig(INDOOR_GEOLOCATION_CONFIG);
            } else {
                console.log('Ambiente outdoor detectado, usando configuração padrão');
                this.setConfig(DEFAULT_GEOLOCATION_CONFIG);
            }
        } catch (error) {
            console.warn('Erro ao detectar ambiente, usando configuração padrão:', error);
            this.setConfig(DEFAULT_GEOLOCATION_CONFIG);
        }
    }

    /**
     * Detecta se estamos em ambiente indoor baseado na precisão do GPS
     */
    private async detectIndoorEnvironment(): Promise<boolean> {
        try {
            // Tentar obter uma posição rápida para testar a qualidade do sinal
            const testPosition = await this.tryGetPosition({
                enableHighAccuracy: true,
                timeout: 8000, // Timeout curto para teste
                maximumAge: 0 // Não usar cache
            });

            if (testPosition) {
                // Se a precisão for muito baixa (>100m), provavelmente estamos indoor
                const isLowAccuracy = testPosition.precisao > 100;
                console.log(`Precisão detectada: ${testPosition.precisao}m - Indoor: ${isLowAccuracy}`);
                return isLowAccuracy;
            }
        } catch (error) {
            // Se falhar rapidamente, provavelmente estamos indoor
            console.log('Falha rápida na detecção de posição, assumindo ambiente indoor');
            return true;
        }

        return false;
    }

    /**
     * Solicita permissões de localização
     */
    async requestPermissions(): Promise<boolean> {
        try {
            if (this.isNative) {
                const permissions = await Geolocation.requestPermissions();
                return permissions.location === 'granted';
            } else {
                // Para web, verifica se a API está disponível
                if ('geolocation' in navigator) {
                    // No navegador, tentamos obter a localização para validar permissão
                    return new Promise((resolve) => {
                        navigator.geolocation.getCurrentPosition(
                            () => resolve(true),
                            (error) => {
                                console.warn('Permissão de geolocalização não concedida:', error.message);
                                // Dependendo do erro, pode ser necessário solicitar permissão explicitamente
                                if (error.code === error.PERMISSION_DENIED) {
                                    console.error('Usuário negou acesso à localização');
                                } else if (error.code === error.POSITION_UNAVAILABLE) {
                                    console.error('Localização indisponível');
                                } else if (error.code === error.TIMEOUT) {
                                    console.error('Timeout ao obter localização');
                                }
                                resolve(false);
                            },
                            {
                                timeout: this.config.availabilityCheckTimeout,
                                enableHighAccuracy: false,
                                maximumAge: this.config.fallbackMaxAge
                            }
                        );
                    });
                }
                return false;
            }
        } catch (error) {
            console.error('Erro ao solicitar permissões de localização:', error);
            return false;
        }
    }

    /**
     * Verifica se as permissões estão concedidas
     */
    async verificarPermissoes(): Promise<boolean> {
        try {
            if (this.isNative) {
                const permissions = await Geolocation.checkPermissions();
                return permissions.location === 'granted';
            } else {
                // Para web, assume que está disponível se a API existir
                return 'geolocation' in navigator;
            }
        } catch (error) {
            console.error('Erro ao verificar permissões:', error);
            return false;
        }
    }

    /**
     * Obtém a posição atual única com retry e fallback
     */
    async getCurrentPosition(): Promise<Localizacao | null> {
        try {
            if (this.isNative) {
                const position = await Geolocation.getCurrentPosition({
                    enableHighAccuracy: true,
                    timeout: this.config.highAccuracyTimeout
                });

                const location: Localizacao = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    velocidade: position.coords.speed || 0,
                    direcao: position.coords.heading || 0,
                    precisao: position.coords.accuracy,
                    timestamp: new Date(position.timestamp)
                };

                this.currentLocationSubject.next(location);
                return location;
            } else {
                // Para web, implementar strategy com fallback
                return await this.getCurrentPositionWithFallback();
            }
        } catch (error) {
            console.error('Erro ao obter localização atual:', error);
            return null;
        }
    }

    /**
     * Obtém posição atual com fallback para baixa precisão
     */
    private async getCurrentPositionWithFallback(): Promise<Localizacao | null> {
        // Primeira tentativa: alta precisão com timeout mais longo
        try {
            const location = await this.tryGetPosition({
                enableHighAccuracy: true,
                timeout: this.config.highAccuracyTimeout,
                maximumAge: this.config.highAccuracyMaxAge
            });
            if (location) {
                console.log('Posição obtida com alta precisão:', location);
                return location;
            }
        } catch (error) {
            console.warn('Falha na obtenção com alta precisão:', error instanceof Error ? error.message : String(error));
        }

        // Segunda tentativa: baixa precisão com timeout reduzido
        try {
            const location = await this.tryGetPosition({
                enableHighAccuracy: false, // Desabilitar alta precisão
                timeout: this.config.lowAccuracyTimeout,
                maximumAge: this.config.lowAccuracyMaxAge
            });
            if (location) {
                console.log('Posição obtida com baixa precisão (fallback):', location);
                return location;
            }
        } catch (error) {
            console.warn('Falha na obtenção com baixa precisão:', error instanceof Error ? error.message : String(error));
        }

        // Terceira tentativa: usar cache mais antigo se disponível
        try {
            const location = await this.tryGetPosition({
                enableHighAccuracy: false,
                timeout: this.config.lowAccuracyTimeout,
                maximumAge: this.config.fallbackMaxAge
            });
            if (location) {
                console.log('Posição obtida de cache antigo (último recurso):', location);
                return location;
            }
        } catch (error) {
            console.error('Todas as tentativas de geolocalização falharam:', error instanceof Error ? error.message : String(error));
        }

        return null;
    }

    /**
     * Tentativa de obter posição com configurações específicas
     */
    private async tryGetPosition(options: PositionOptions): Promise<Localizacao | null> {
        return new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const location: Localizacao = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        velocidade: (position.coords.speed || 0) * 3.6, // Converter para km/h
                        direcao: position.coords.heading || 0,
                        precisao: position.coords.accuracy,
                        timestamp: new Date(position.timestamp)
                    };

                    this.currentLocationSubject.next(location);
                    resolve(location);
                },
                (error) => {
                    reject(error);
                },
                options
            );
        });
    }

    /**
     * Inicia o monitoramento contínuo da localização
     */
    async startWatching(): Promise<boolean> {
        try {
            console.log('GeolocationService.startWatching() iniciado');

            if (this.isTracking) {
                console.log('Monitoramento já está ativo');
                return true;
            }

            // Configurar automaticamente para o ambiente
            await this.autoConfigureForEnvironment();

            // Verificar se a geolocalização está disponível
            if (!this.isNative) {
                console.log('Verificando disponibilidade da geolocalização no navegador...');
                const available = await this.isGeolocationAvailable();
                if (!available) {
                    console.error('Geolocalização não está disponível ou funcionando');
                    return false;
                }
                console.log('Geolocalização disponível no navegador');
            }

            console.log('Verificando permissões...');
            const hasPermission = await this.verificarPermissoes();
            if (!hasPermission) {
                console.log('Permissões não concedidas, solicitando...');
                const granted = await this.requestPermissions();
                if (!granted) {
                    console.error('Permissões negadas pelo usuário');
                    return false;
                }
                console.log('Permissões concedidas');
            } else {
                console.log('Permissões já concedidas');
            }

            if (this.isNative) {
                console.log('Iniciando monitoramento no modo nativo...');
                // Versão para dispositivo móvel
                this.watchId = await Geolocation.watchPosition({
                    enableHighAccuracy: true,
                    timeout: this.config.watchTimeout
                }, (position, err) => {
                    if (err) {
                        console.error('Erro no monitoramento de localização:', err);
                        return;
                    }

                    if (position) {
                        console.log('Nova posição recebida (nativo):', position);
                        const location: Localizacao = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            velocidade: position.coords.speed || 0,
                            direcao: position.coords.heading || 0,
                            precisao: position.coords.accuracy,
                            timestamp: new Date(position.timestamp)
                        };

                        this.currentLocationSubject.next(location);
                    }
                });
            } else {
                console.log('Iniciando monitoramento no modo web...');
                // Versão para navegador web
                this.watchId = navigator.geolocation.watchPosition(
                    (position) => {
                        console.log('Nova posição recebida (web):', position);
                        const location: Localizacao = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            velocidade: (position.coords.speed || 0) * 3.6, // Converter para km/h
                            direcao: position.coords.heading || 0,
                            precisao: position.coords.accuracy,
                            timestamp: new Date(position.timestamp)
                        };

                        this.currentLocationSubject.next(location);
                    },
                    (error) => {
                        console.error('Erro no monitoramento de localização (web):', error);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: this.config.watchTimeout,
                        maximumAge: this.config.highAccuracyMaxAge
                    }
                );
            }

            this.isTracking = true;
            console.log('Monitoramento iniciado com sucesso. Watch ID:', this.watchId);
            return true;
        } catch (error) {
            console.error('Erro ao iniciar monitoramento de localização:', error);
            return false;
        }
    }

    /**
     * Para o monitoramento da localização
     */
    async stopWatching(): Promise<void> {
        if (this.watchId !== null) {
            if (this.isNative) {
                await Geolocation.clearWatch({ id: this.watchId as string });
            } else {
                navigator.geolocation.clearWatch(this.watchId as number);
            }
            this.watchId = null;
        }
        this.isTracking = false;
    }

    /**
     * Verifica se está monitorando
     */
    get estaMonitorando(): boolean {
        return this.isTracking;
    }

    /**
     * Calcula a distância entre duas coordenadas (em metros)
     */
    calcularDistancia(
        lat1: number,
        lon1: number,
        lat2: number,
        lon2: number
    ): number {
        const R = 6371e3; // Raio da Terra em metros
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δφ = (lat2 - lat1) * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * Calcula o bearing (direção) entre duas coordenadas
     */
    calcularBearing(
        lat1: number,
        lon1: number,
        lat2: number,
        lon2: number
    ): number {
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const y = Math.sin(Δλ) * Math.cos(φ2);
        const x = Math.cos(φ1) * Math.sin(φ2) -
            Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

        const θ = Math.atan2(y, x);
        return (θ * 180 / Math.PI + 360) % 360;
    }

    /**
     * Converte velocidade de m/s para km/h
     */
    converterVelocidadeParaKmh(velocidadeMps: number): number {
        return velocidadeMps * 3.6;
    }

    /**
     * Converte posição do Capacitor para modelo da aplicação
     */
    private converterPosicaoParaLocalizacao(position: Position): Localizacao {
        return {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            velocidade: this.converterVelocidadeParaKmh(position.coords.speed || 0),
            direcao: position.coords.heading || 0,
            precisao: position.coords.accuracy,
            timestamp: new Date(position.timestamp)
        };
    }

    /**
     * Simula localização para testes (apenas em desenvolvimento)
     */
    simularLocalizacao(lat: number, lon: number, velocidade: number = 0): void {
        const localizacaoSimulada: Localizacao = {
            latitude: lat,
            longitude: lon,
            velocidade: velocidade,
            direcao: 0,
            precisao: 10,
            timestamp: new Date()
        };
        this.currentLocationSubject.next(localizacaoSimulada);
    }

    /**
     * Retorna Observable da localização atual com retry
     */
    getCurrentLocation(): Observable<Localizacao> {
        return new Observable(subscriber => {
            // Verificar se já temos uma localização atual recente (menos de 2 minutos)
            const currentLocation = this.currentLocationSubject.value;
            if (currentLocation) {
                const age = Date.now() - currentLocation.timestamp.getTime();
                if (age < this.config.recentLocationMaxAge) {
                    subscriber.next(currentLocation);
                    subscriber.complete();
                    return;
                }
            }

            // Se não temos localização recente, tentar obter uma nova com retry
            this.getCurrentPositionWithRetry().then(location => {
                if (location) {
                    subscriber.next(location);
                    subscriber.complete();
                } else {
                    subscriber.error(new Error('Não foi possível obter a localização'));
                }
            }).catch(error => {
                subscriber.error(error);
            });
        });
    }

    /**
     * Obtém posição atual com retry automático
     */
    private async getCurrentPositionWithRetry(maxRetries: number = this.config.maxRetries): Promise<Localizacao | null> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            console.log(`Tentativa ${attempt} de ${maxRetries} para obter localização`);

            try {
                const location = await this.getCurrentPosition();
                if (location) {
                    console.log(`Localização obtida na tentativa ${attempt}:`, location);
                    return location;
                }
            } catch (error) {
                console.warn(`Tentativa ${attempt} falhou:`, error instanceof Error ? error.message : String(error));
            }

            // Esperar progressivamente mais tempo entre tentativas
            if (attempt < maxRetries) {
                const delay = attempt * this.config.retryDelay;
                console.log(`Aguardando ${delay}ms antes da próxima tentativa...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        console.error(`Falha ao obter localização após ${maxRetries} tentativas`);
        return null;
    }

    /**
     * Retorna Observable que monitora mudanças de posição
     */
    watchPosition(): Observable<Localizacao> {
        console.log('GeolocationService.watchPosition() chamado');
        console.log('Estado atual - isTracking:', this.isTracking);
        console.log('Localização atual:', this.currentLocationSubject.value);

        return this.currentLocation$.pipe(
            // Filtrar valores null antes de processar
            filter((location): location is Localizacao => {
                console.log('Filtrando localização:', location);
                return location !== null;
            }),
            catchError(error => {
                console.error('Erro no watchPosition:', error);
                // Em caso de erro, tentar reiniciar o monitoramento
                if (!this.isTracking) {
                    console.log('Tentando reiniciar monitoramento...');
                    this.startWatching().catch(startError => {
                        console.error('Erro ao reiniciar monitoramento:', startError);
                    });
                }
                // Retornar observable vazio para não quebrar a cadeia
                return of();
            })
        );
    }

    /**
     * Verifica se a geolocalização está disponível e funcionando
     */
    async isGeolocationAvailable(): Promise<boolean> {
        try {
            // Verificar se a API está disponível
            if (!('geolocation' in navigator)) {
                console.error('Geolocalização não está disponível neste navegador');
                return false;
            }

            // Verificar se estamos em contexto seguro (HTTPS ou localhost)
            if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                console.warn('Geolocalização pode não funcionar em contextos não seguros (HTTP)');
            }

            // Tentar obter uma posição para verificar se está funcionando
            return new Promise((resolve) => {
                navigator.geolocation.getCurrentPosition(
                    () => resolve(true),
                    (error) => {
                        console.error('Erro ao verificar disponibilidade da geolocalização:', error.message);
                        // Se o erro for apenas timeout, ainda consideramos disponível
                        if (error.code === error.TIMEOUT) {
                            console.warn('Timeout na verificação, mas geolocalização está disponível');
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    },
                    {
                        timeout: this.config.availabilityCheckTimeout,
                        enableHighAccuracy: false,
                        maximumAge: this.config.fallbackMaxAge
                    }
                );
            });
        } catch (error) {
            console.error('Erro ao verificar geolocalização:', error);
            return false;
        }
    }

    /**
     * Obtém informações de diagnóstico sobre o estado da geolocalização
     */
    async getDiagnosticInfo(): Promise<{
        isAvailable: boolean;
        hasPermission: boolean;
        isTracking: boolean;
        currentLocation: Localizacao | null;
        lastError?: string;
        config: GeolocationConfig;
    }> {
        const isAvailable = await this.isGeolocationAvailable();
        const hasPermission = await this.verificarPermissoes();

        return {
            isAvailable,
            hasPermission,
            isTracking: this.isTracking,
            currentLocation: this.currentLocationSubject.value,
            config: this.config
        };
    }

    /**
     * Força uma nova tentativa de obter localização com configuração específica
     */
    async forceLocationUpdate(useIndoorConfig: boolean = false): Promise<Localizacao | null> {
        const originalConfig = { ...this.config };

        try {
            if (useIndoorConfig) {
                this.setConfig(INDOOR_GEOLOCATION_CONFIG);
            }

            const location = await this.getCurrentPositionWithRetry();
            return location;
        } finally {
            // Restaurar configuração original
            this.config = originalConfig;
        }
    }
}

# Melhorias na Geolocalização - RadarBrasil

## Problema Resolvido

O erro "Position acquisition timed out" estava ocorrendo devido a timeouts muito baixos (10 segundos) e falta de estratégias de fallback para ambientes com GPS fraco ou indoor.

## Soluções Implementadas

### 1. Sistema de Configuração Flexível

Criado arquivo `src/app/core/config/geolocation.config.ts` com três perfis de configuração:

- **DEFAULT_GEOLOCATION_CONFIG**: Configuração padrão balanceada
- **INDOOR_GEOLOCATION_CONFIG**: Otimizada para ambientes fechados
- **FAST_GEOLOCATION_CONFIG**: Otimizada para obtenção rápida

### 2. Timeouts Aumentados

- **Alta precisão**: 25 segundos (era 10s)
- **Baixa precisão**: 15 segundos (era 10s)
- **Monitoramento**: 45 segundos (era 30s)
- **Verificação**: 10 segundos (era 5s)

### 3. Sistema de Fallback Inteligente

Implementado em `getCurrentPositionWithFallback()`:

1. **Primeira tentativa**: Alta precisão (25s timeout)
2. **Segunda tentativa**: Baixa precisão (15s timeout)
3. **Terceira tentativa**: Cache antigo (até 10 minutos)

### 4. Retry Automático

- Até 3 tentativas por padrão
- Delay progressivo entre tentativas (2s, 4s, 6s)
- Configurável via `maxRetries` e `retryDelay`

### 5. Detecção Automática de Ambiente

- Detecta automaticamente se está em ambiente indoor
- Aplica configuração otimizada baseada na precisão do GPS
- Fallback para configuração indoor em caso de falha rápida

### 6. Melhor Tratamento de Erros

- Diagnóstico detalhado do estado da geolocalização
- Mensagens de erro específicas para cada situação
- Opção de retry com configuração indoor
- Feedback claro para o usuário

### 7. Cache Inteligente

- Localização "recente" válida por 2 minutos
- Cache de baixa precisão válido por 2 minutos
- Cache de fallback válido por 10 minutos

## Novos Métodos Disponíveis

### GeolocationService

```typescript
// Configurar manualmente
setConfig(config: Partial<GeolocationConfig>): void

// Configuração automática baseada no ambiente
autoConfigureForEnvironment(): Promise<void>

// Diagnóstico completo
getDiagnosticInfo(): Promise<DiagnosticInfo>

// Forçar atualização com configuração específica
forceLocationUpdate(useIndoorConfig: boolean): Promise<Localizacao | null>
```

### AlertService

```typescript
// Novo método de confirmação
showConfirm(header: string, message: string): Promise<boolean>
```

## Como Usar

### Configuração Manual

```typescript
// Para ambiente indoor
this.geolocationService.setConfig(INDOOR_GEOLOCATION_CONFIG);

// Para obtenção rápida
this.geolocationService.setConfig(FAST_GEOLOCATION_CONFIG);

// Configuração customizada
this.geolocationService.setConfig({
  highAccuracyTimeout: 30000,
  maxRetries: 5
});
```

### Configuração Automática

```typescript
// O serviço detecta automaticamente o ambiente
await this.geolocationService.autoConfigureForEnvironment();
```

### Diagnóstico

```typescript
const diagnostic = await this.geolocationService.getDiagnosticInfo();
console.log('Estado da geolocalização:', diagnostic);
```

## Benefícios

1. **Redução de timeouts**: Timeouts mais longos evitam falhas prematuras
2. **Melhor experiência indoor**: Configuração específica para ambientes fechados
3. **Recuperação automática**: Sistema de retry e fallback
4. **Feedback claro**: Usuário entende o que está acontecendo
5. **Flexibilidade**: Configurações adaptáveis a diferentes cenários
6. **Diagnóstico**: Facilita debugging e suporte

## Configurações Recomendadas

### Para Aplicações Móveis

- Use `DEFAULT_GEOLOCATION_CONFIG` como base
- Configure `autoConfigureForEnvironment()` no início

### Para Ambientes Indoor

- Use `INDOOR_GEOLOCATION_CONFIG`
- Aumente `maxRetries` para 5
- Considere `fallbackMaxAge` de 15 minutos

### Para Aplicações que Precisam de Velocidade

- Use `FAST_GEOLOCATION_CONFIG`
- Reduza `maxRetries` para 2
- Use `recentLocationMaxAge` de 1 minuto

## Monitoramento

O sistema agora fornece logs detalhados para monitoramento:

```
Tentativa 1 de 3 para obter localização
Posição obtida com alta precisão: {...}
Ambiente indoor detectado, aplicando configuração otimizada
Configuração de geolocalização atualizada: {...}
```

## Compatibilidade

- ✅ Navegadores modernos (Chrome, Firefox, Safari, Edge)
- ✅ Dispositivos móveis (iOS, Android)
- ✅ Capacitor/Ionic
- ✅ HTTPS e localhost
- ⚠️ HTTP (funcionalidade limitada)

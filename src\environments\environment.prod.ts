export const environment = {
    production: true,
    apiUrl: 'https://api.radarbrasil.com',
    appName: 'Radar Brasil',
    version: '1.0.0',
    features: {
        enableConsoleLog: false,
        enableGeolocationSimulation: false,
        enableOfflineMode: true,
        debugMode: false
    },
    maps: {
        defaultZoom: 15,
        maxZoom: 18,
        minZoom: 8,
        tileLayerUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    },
    radar: {
        defaultDetectionRadius: 5000, // metros
        alertDistances: {
            far: 1000,    // 1km
            medium: 500,  // 500m
            near: 200,    // 200m
            critical: 100 // 100m
        },
        updateInterval: 10000, // ms (mais lento em produção)
        cacheTimeout: 3600000 // 1 hora em ms
    },
    notifications: {
        enableVibration: true,
        enableSound: true,
        soundFile: 'assets/sounds/radar-alert.mp3'
    },
    mapTileUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    mapAttribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
};

export const environment = {
    production: false,
    apiUrl: 'http://localhost:3000',
    appName: 'Radar Brasil',
    version: '1.0.0',
    features: {
        enableConsoleLog: true,
        enableGeolocationSimulation: true,
        enableOfflineMode: true,
        debugMode: true
    },
    maps: {
        defaultZoom: 15,
        maxZoom: 18,
        minZoom: 8,
        tileLayerUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    },
    radar: {
        defaultDetectionRadius: 5000, // metros
        alertDistances: {
            far: 1000,    // 1km
            medium: 500,  // 500m
            near: 200,    // 200m
            critical: 100 // 100m
        },
        updateInterval: 5000, // ms
        cacheTimeout: 3600000 // 1 hora em ms
    },
    notifications: {
        enableVibration: true,
        enableSound: true,
        soundFile: 'assets/sounds/radar-alert.mp3'
    },
    mapTileUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    mapAttribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
};

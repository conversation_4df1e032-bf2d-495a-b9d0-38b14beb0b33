import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { MapComponent } from '../../shared/components/map/map.component';
import { GeolocationService } from '../../core/services/geolocation.service';
import { AlertService } from '../../core/services/alert.service';

@Component({
    selector: 'app-home',
    templateUrl: './home.page.html',
    styleUrls: ['./home.page.scss'],
    standalone: true,
    imports: [
        IonContent,
        IonHeader,
        IonTitle,
        IonToolbar,
        CommonModule,
        FormsModule,
        MapComponent
    ]
})
export class HomePage implements OnInit {

    constructor(
        private geolocationService: GeolocationService,
        private alertService: AlertService
    ) { }

    async ngOnInit() {
        await this.initializeLocation();
    }

    private async initializeLocation(): Promise<void> {
        try {
            // Verificar permissões
            const hasPermission = await this.geolocationService.verificarPermissoes();

            if (!hasPermission) {
                const granted = await this.geolocationService.requestPermissions();
                if (!granted) {
                    this.alertService.showAlert(
                        'Permissão Necessária',
                        'O RadarBrasil precisa de acesso à sua localização para funcionar corretamente.'
                    );
                    return;
                }
            }

            // Iniciar monitoramento
            const success = await this.geolocationService.startWatching();
            if (!success) {
                this.alertService.showAlert(
                    'Erro de Localização',
                    'Não foi possível iniciar o monitoramento de localização.'
                );
            }
        } catch (error) {
            console.error('Erro ao inicializar localização:', error);
            this.alertService.showAlert(
                'Erro',
                'Ocorreu um erro ao configurar a localização.'
            );
        }
    }

}

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Capacitor } from '@capacitor/core';
import {
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonList,
    IonItem,
    IonLabel,
    IonToggle,
    IonButton,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonText,
    IonIcon
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { locationOutline, checkmarkCircleOutline, closeCircleOutline } from 'ionicons/icons';
import { GeolocationService } from '../../core/services/geolocation.service';

@Component({
    selector: 'app-settings',
    templateUrl: './settings.page.html',
    styleUrls: ['./settings.page.scss'],
    standalone: true,
    imports: [IonContent, IonHeader, IonTitle, IonToolbar, IonList, IonItem, IonLabel, IonToggle, CommonModule, FormsModule]
})
export class SettingsPage {

    constructor() { }

}

.map-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.map-leaflet {
    width: 100%;
    height: 100%;
    min-height: 300px;
    z-index: 1;
}

.map-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1000;

    ion-fab {
        pointer-events: auto;
    }

    ion-fab-button {
        --background: var(--ion-color-primary);
        --color: white;
        --box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

        &:hover {
            --background: var(--ion-color-primary-shade);
        }
    }
}

// Estilos específicos para os popups do Leaflet
:host ::ng-deep {
    .leaflet-popup-content-wrapper {
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        background: white;
    }

    .leaflet-popup-content {
        margin: 16px 20px;
        line-height: 1.5;
        font-size: 14px;

        .user-popup {
            text-align: center;

            strong {
                color: var(--ion-color-primary);
                font-weight: 600;
                font-size: 16px;
            }

            .speed {
                color: var(--radar-speed-color, #00e676);
                font-weight: 500;
            }

            .accuracy {
                color: var(--ion-color-medium);
            }

            small {
                color: var(--ion-color-medium);
                font-size: 12px;
            }
        }

        .radar-popup {
            text-align: center;
            min-width: 200px;

            strong {
                color: var(--radar-danger-color, #f44336);
                font-weight: 600;
                font-size: 16px;
                display: block;
                margin-bottom: 8px;
            }

            .speed-limit {
                color: var(--radar-danger-color, #f44336);
                font-weight: 600;
                font-size: 15px;
            }

            .direction {
                color: var(--ion-color-dark);
                font-weight: 500;
            }

            .address {
                color: var(--ion-color-medium);
                font-size: 13px;
                font-style: italic;
            }

            small {
                color: var(--ion-color-medium);
                font-size: 11px;
            }
        }
    }

    .leaflet-popup-tip {
        background: white;
        box-shadow: 0 3px 14px rgba(0, 0, 0, 0.15);
    }

    // Customizar controles de zoom
    .leaflet-control-zoom {
        a {
            background-color: var(--ion-color-light);
            color: var(--ion-color-dark);
            border: 1px solid var(--ion-color-medium);
            border-radius: 4px;

            &:hover {
                background-color: var(--ion-color-primary);
                color: white;
            }
        }
    }

    // Estilos para marcadores customizados
    .user-location-icon {
        .user-marker {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease-out;

            .user-marker-arrow {
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;

                .arrow-head {
                    width: 0;
                    height: 0;
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    border-bottom: 16px solid #2196F3;
                    position: relative;
                    z-index: 3;
                    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
                }

                .arrow-body {
                    width: 6px;
                    height: 8px;
                    background-color: #2196F3;
                    margin: -2px auto 0;
                    border-radius: 0 0 3px 3px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                }

                &.moving {
                    .arrow-head {
                        border-bottom-color: #4CAF50;
                        animation: arrowPulse 1.5s infinite;
                    }

                    .arrow-body {
                        background-color: #4CAF50;
                    }
                }
            }

            .user-marker-pulse {
                position: absolute;
                width: 32px;
                height: 32px;
                background-color: rgba(33, 150, 243, 0.2);
                border-radius: 50%;
                animation: pulse 2s infinite;
                z-index: 1;
            }

            .speed-indicator {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 2px 6px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
                white-space: nowrap;
                z-index: 4;
            }
        }

        &.moving .user-marker .user-marker-pulse {
            background-color: rgba(76, 175, 80, 0.2);
            animation: movingPulse 1.5s infinite;
        }

        &.stationary .user-marker .user-marker-arrow {
            .arrow-head {
                border-bottom-color: #FF9800;
            }

            .arrow-body {
                background-color: #FF9800;
            }
        }
    }

    .radar-icon {
        .radar-marker {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .radar-icon {
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, #FF5722, #F44336);
                border: 3px solid white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
                animation: radarPulse 3s infinite;
            }
        }
    }

    // Animações
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }

        50% {
            transform: scale(1.2);
            opacity: 0.7;
        }

        100% {
            transform: scale(1.4);
            opacity: 0;
        }
    }

    @keyframes movingPulse {
        0% {
            transform: scale(1);
            opacity: 0.8;
        }

        50% {
            transform: scale(1.3);
            opacity: 0.4;
        }

        100% {
            transform: scale(1.6);
            opacity: 0;
        }
    }

    @keyframes arrowPulse {

        0%,
        100% {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        50% {
            filter: drop-shadow(0 4px 8px rgba(76, 175, 80, 0.6));
        }
    }

    @keyframes radarPulse {

        0%,
        100% {
            transform: scale(1);
            box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4);
        }

        50% {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(255, 87, 34, 0.6);
        }
    }

    // Estilos para modo escuro
    @media (prefers-color-scheme: dark) {
        .leaflet-popup-content-wrapper {
            background-color: var(--ion-color-dark);
            color: var(--ion-color-light);
        }

        .leaflet-popup-tip {
            background: var(--ion-color-dark);
        }

        .leaflet-control-zoom a {
            background-color: var(--ion-color-dark);
            color: var(--ion-color-light);
            border: 1px solid var(--ion-color-medium);
        }

        .user-marker-inner {
            border-color: rgba(255, 255, 255, 0.8);
        }

        .radar-marker .radar-icon {
            border-color: rgba(255, 255, 255, 0.8);
        }
    }

    // Estilos para clusters de marcadores
    .marker-cluster-small {
        background-color: rgba(33, 150, 243, 0.6);
        color: white;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .marker-cluster-medium {
        background-color: rgba(255, 152, 0, 0.6);
        color: white;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .marker-cluster-large {
        background-color: rgba(244, 67, 54, 0.6);
        color: white;
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .marker-cluster {
        border: 3px solid white;

        div {
            width: 30px;
            height: 30px;
            margin-left: 5px;
            margin-top: 5px;
            text-align: center;
            border-radius: 15px;
            font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
        }

        span {
            line-height: 30px;
        }
    }
}

// Responsividade
@media (max-width: 768px) {
    .map-container {
        border-radius: 0;
    }

    .map-controls {
        ion-fab-button {
            --size: 40px;
        }
    }

    :host ::ng-deep {
        .leaflet-popup-content {
            margin: 12px 16px;
            font-size: 13px;

            .radar-popup,
            .user-popup {
                min-width: 180px;
            }
        }

        .user-location-icon .user-marker {
            .user-marker-arrow {
                .arrow-head {
                    border-left-width: 6px;
                    border-right-width: 6px;
                    border-bottom-width: 12px;
                }

                .arrow-body {
                    width: 4px;
                    height: 6px;
                }
            }

            .user-marker-pulse {
                width: 24px;
                height: 24px;
            }

            .speed-indicator {
                font-size: 9px;
                padding: 1px 4px;
                bottom: -16px;
            }
        }

        .radar-icon .radar-marker .radar-icon {
            width: 28px;
            height: 28px;
            font-size: 14px;
        }
    }
}

// Status de navegação
.navigation-status {
    position: absolute;
    top: 16px;
    left: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 180px;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;

        ion-icon {
            font-size: 16px;
            min-width: 16px;
        }

        span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &:hover {
            transform: translateX(2px);
        }
    }

    @media (prefers-color-scheme: dark) {
        background: rgba(0, 0, 0, 0.85);
        color: white;
    }
}

// Status de localização (mantido para compatibilidade)
.location-status {
    position: absolute;
    top: 16px;
    left: 16px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;

        ion-icon {
            font-size: 16px;
        }
    }

    @media (prefers-color-scheme: dark) {
        background: rgba(0, 0, 0, 0.85);
        color: white;
    }
}

// Estados de carregamento
.map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    background-color: var(--ion-color-light);
    color: var(--ion-color-medium);
    flex-direction: column;

    ion-spinner {
        margin-bottom: 16px;
    }

    .loading-text {
        font-size: 14px;
        text-align: center;
    }
}

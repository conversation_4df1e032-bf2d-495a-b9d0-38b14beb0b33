# RadarBrasil - Detector de Radares

## 📱 Visão Geral

Aplicativo mobile para Android desenvolvido em Angular/Ionic que detecta radares de velocidade, oferece navegação GPS e alerta sobre limites de velocidade.

## 🛠️ Tecnologias Utilizadas

### Frontend

- **Angular 17+** - Framework principal
- **Ionic 7+** - Framework mobile multiplataforma
- **Capacitor** - Ponte nativa para Android
- **TypeScript** - Linguagem principal
- **RxJS** - Programação reativa
- **Angular Material** - Componentes UI

### Mapas e Geolocalização

- **Leaflet** - Biblioteca de mapas
- **OpenStreetMap** - Dados de mapa gratuitos
- **Capacitor Geolocation** - Plugin GPS
- **Turf.js** - Cálculos geoespaciais

### APIs e Dados

- **JSON Server** - API fake para desenvolvimento
- **Angular HTTP Client** - Cliente HTTP
- **IndexedDB/SQLite** - Armazenamento local

### Build e Deploy

- **Capacitor Android** - Build nativo
- **Android Studio** - IDE para build final

## 🏗️ Arquitetura do Projeto

```
src/
├── app/
│   ├── core/                    # Módulo principal
│   │   ├── services/           # Serviços globais
│   │   ├── guards/             # Guards de rota
│   │   ├── interceptors/       # HTTP interceptors
│   │   └── models/             # Interfaces e modelos
│   ├── shared/                 # Componentes compartilhados
│   │   ├── components/         # Componentes reutilizáveis
│   │   ├── pipes/              # Pipes customizados
│   │   └── directives/         # Diretivas customizadas
│   ├── features/               # Módulos por funcionalidade
│   │   ├── map/                # Módulo do mapa
│   │   ├── radar/              # Módulo de radares
│   │   ├── navigation/         # Módulo de navegação
│   │   ├── alerts/             # Módulo de alertas
│   │   └── settings/           # Configurações
│   └── pages/                  # Páginas principais
│       ├── home/               # Tela principal
│       ├── radar-list/         # Lista de radares
│       └── settings/           # Configurações
├── assets/                     # Recursos estáticos
├── environments/               # Configurações de ambiente
└── theme/                      # Temas e estilos
```

## 🎯 Funcionalidades Principais

### 1. **Mapa e Navegação**

- Exibição de mapa interativo
- Localização atual do usuário
- Direcionamento GPS em tempo real
- Cálculo de rotas
- Indicador de direção e velocidade

### 2. **Detecção de Radares**

- Consulta à API de radares cadastrados
- Exibição de radares no mapa
- Cálculo de distância até radares
- Classificação por tipo de radar

### 3. **Sistema de Alertas**

- Alerta de proximidade de radar
- Notificação de limite de velocidade
- Alerta de excesso de velocidade
- Notificações sonoras e visuais

### 4. **Gerenciamento de Dados**

- Cache local de radares
- Sincronização com API
- Histórico de viagens
- Configurações do usuário

## 📊 Modelos de Dados

### Radar

```typescript
interface Radar {
  id: string;
  latitude: number;
  longitude: number;
  tipo: 'fixo' | 'movel' | 'semaforo';
  velocidadeMaxima: number;
  direcao?: 'norte' | 'sul' | 'leste' | 'oeste';
  ativo: boolean;
  dataAtualizacao: Date;
}
```

### Localização

```typescript
interface Localizacao {
  latitude: number;
  longitude: number;
  velocidade: number;
  direcao: number;
  precisao: number;
  timestamp: Date;
}
```

### Alerta

```typescript
interface Alerta {
  id: string;
  tipo: 'radar' | 'velocidade';
  mensagem: string;
  radar?: Radar;
  distancia?: number;
  ativo: boolean;
}
```

## 🔄 Fluxo da Aplicação

1. **Inicialização**
   - Solicitar permissões de localização
   - Carregar configurações do usuário
   - Inicializar serviços de mapa e GPS

2. **Operação Principal**
   - Monitorar localização GPS
   - Consultar radares próximos
   - Calcular distâncias e alertas
   - Atualizar interface do usuário

3. **Gerenciamento de Alertas**
   - Detectar proximidade de radares
   - Comparar velocidade atual com limite
   - Emitir alertas visuais e sonoros
   - Registrar histórico de alertas

## 📱 Páginas e Componentes

### Páginas Principais

- **HomePage** - Mapa principal com navegação
- **RadarListPage** - Lista de radares próximos
- **SettingsPage** - Configurações do app
- **AboutPage** - Informações sobre o app

### Componentes Principais

- **MapComponent** - Componente do mapa
- **RadarMarkerComponent** - Marcadores de radar
- **AlertComponent** - Sistema de alertas
- **SpeedDisplayComponent** - Mostrador de velocidade
- **NavigationComponent** - Componente de navegação

## 🔧 Configuração e Instalação

### Pré-requisitos

- Node.js 18+
- Angular CLI
- Ionic CLI
- Android Studio
- SDK Android

### Comandos de Instalação

```bash
# Instalar dependências globais
npm install -g @angular/cli @ionic/cli

# Criar projeto
ionic start radar-brasil tabs --type=angular

# Adicionar plataforma Android
ionic capacitor add android

# Instalar dependências específicas
npm install leaflet @types/leaflet turf @turf/turf
npm install @capacitor/geolocation @capacitor/local-notifications
```

## 🚀 Cronograma de Desenvolvimento

### Fase 1 - Configuração Base (1-2 semanas)

- [X] Setup do projeto Ionic/Angular
- [X] Configuração do ambiente de desenvolvimento
- [X] Estrutura de pastas e arquitetura
- [X] Configuração do Capacitor

### Fase 2 - Funcionalidades Core (2-3 semanas)

- [X] Implementação do mapa (Leaflet)
- [X] Serviço de geolocalização
- [X] API fake para radares
- [X] Modelos de dados

### Fase 3 - Detecção e Alertas (3-4 semanas)

- [ ] Sistema de detecção de radares
- [ ] Cálculos de distância e velocidade
- [ ] Sistema de alertas
- [ ] Interface de usuário

### Fase 4 - Otimização e Deploy (1-2 semanas)

- [ ] Testes e otimizações
- [ ] Build para Android
- [ ] Testes no dispositivo
- [ ] Documentação final

## 🧪 Testes

### Tipos de Teste

- **Unitários** - Jest + Jasmine
- **Integração** - Cypress
- **E2E** - Protractor/Cypress
- **Dispositivo** - Testes manuais em Android

### Cenários de Teste

- Precisão do GPS
- Detecção de radares
- Sistemas de alerta
- Performance do mapa
- Consumo de bateria

## 📦 Deploy e Distribuição

### Build de Produção

```bash
# Build da aplicação
ionic build --prod

# Sincronizar com Capacitor
ionic capacitor sync android

# Abrir no Android Studio
ionic capacitor open android
```

### Distribuição

- Google Play Store (produção)
- APK direto (testes)
- Firebase App Distribution (beta)

## 🔐 Considerações de Segurança

- Criptografia de dados sensíveis
- Validação de dados da API
- Permissões mínimas necessárias
- Proteção contra ataques de geolocalização

## 📊 Métricas e Analytics

- Uso do aplicativo
- Precisão dos alertas
- Performance do GPS
- Crashes e erros
- Satisfação do usuário

## 🎨 Design e UX

### Princípios

- Interface minimalista para uso durante condução
- Alto contraste para visibilidade
- Comandos por voz quando possível
- Modo noturno automático

### Cores Principais

- Primária: #2196F3 (Azul)
- Secundária: #FF5722 (Laranja para alertas)
- Sucesso: #4CAF50 (Verde)
- Perigo: #F44336 (Vermelho)

### Ícones

- Material Design Icons
- Ícones específicos para tipos de radar
- Indicadores de velocidade
- Setas de direção

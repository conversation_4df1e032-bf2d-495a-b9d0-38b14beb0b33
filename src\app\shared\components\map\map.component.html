<div class="map-container">
    <!-- Indicador de carregamento -->
    <div class="map-loading" *ngIf="!mapLoaded">
        <ion-spinner name="crescent" color="primary"></ion-spinner>
        <div class="loading-text">
            <strong>Carregando mapa...</strong><br>
            <small>Aguarde enquanto carregamos os dados</small>
        </div>
    </div>

    <!-- Container do mapa -->
    <div #mapContainer class="map-leaflet" [style.opacity]="mapLoaded ? '1' : '0'"></div>

    <!-- Controles flutuantes -->
    <div class="map-controls" *ngIf="showControls && mapLoaded">
        <!-- Botões do lado direito -->
        <ion-fab vertical="bottom" horizontal="end" slot="fixed">
            <ion-fab-button size="small" (click)="centerOnUser()" color="primary">
                <ion-icon name="locate"></ion-icon>
            </ion-fab-button>

            <ion-fab-list side="top">
                <!-- Botão modo navegação -->
                <ion-fab-button size="small" (click)="toggleNavigationMode()"
                    [color]="isNavigationMode ? 'success' : 'medium'" title="Modo Navegação">
                    <ion-icon [name]="isNavigationMode ? 'navigate' : 'navigate-outline'"></ion-icon>
                </ion-fab-button>

                <!-- Botão auto-centralizar -->
                <ion-fab-button size="small" (click)="toggleAutoCenter()" [color]="autoCenter ? 'tertiary' : 'medium'"
                    title="Auto-centralizar">
                    <ion-icon [name]="autoCenter ? 'radio-button-on' : 'radio-button-off'"></ion-icon>
                </ion-fab-button>
            </ion-fab-list>
        </ion-fab>

        <!-- Botão toggle radares -->
        <ion-fab vertical="bottom" horizontal="start" slot="fixed">
            <ion-fab-button size="small" (click)="toggleRadars()" [color]="showRadars ? 'danger' : 'medium'">
                <ion-icon [name]="showRadars ? 'radio' : 'radio-outline'"></ion-icon>
            </ion-fab-button>
        </ion-fab>

        <!-- Botão recarregar -->
        <ion-fab vertical="top" horizontal="end" slot="fixed">
            <ion-fab-button size="small" (click)="reloadMap()" color="secondary">
                <ion-icon name="refresh"></ion-icon>
            </ion-fab-button>
        </ion-fab>
    </div>

    <!-- Status de navegação -->
    <div class="navigation-status" *ngIf="showLocationStatus && mapLoaded">
        <div class="status-item">
            <ion-icon name="speedometer" [color]="currentSpeed > 0 ? 'primary' : 'medium'"></ion-icon>
            <span>{{currentSpeed}} km/h</span>
        </div>
        <div class="status-item" *ngIf="currentHeading > 0">
            <ion-icon name="compass" color="secondary"></ion-icon>
            <span>{{getCompassDirection(currentHeading)}}</span>
        </div>
        <div class="status-item">
            <ion-icon name="location" [color]="getNavigationStatus() === 'Navegação' ? 'success' : 'medium'"></ion-icon>
            <span>{{getNavigationStatus()}}</span>
        </div>
        <div class="status-item" *ngIf="nearbyRadarCount > 0">
            <ion-icon name="warning" color="danger"></ion-icon>
            <span>{{nearbyRadarCount}} radar(es)</span>
        </div>
    </div>
</div>

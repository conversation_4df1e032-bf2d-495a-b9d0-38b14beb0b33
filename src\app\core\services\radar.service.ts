import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Radar } from '../models';

@Injectable({
    providedIn: 'root'
})
export class RadarService {
    private readonly apiUrl = 'http://localhost:3000'; // JSON Server URL
    private radaresSubject = new BehaviorSubject<Radar[]>([]);
    public radares$ = this.radaresSubject.asObservable();

    constructor(private http: HttpClient) { }

    async carregarRadares(): Promise<Radar[]> {
        try {
            const radares = await this.http.get<Radar[]>(`${this.apiUrl}/radares`).toPromise() || [];
            this.radaresSubject.next(radares);
            return radares;
        } catch (error) {
            console.error('Erro ao carregar radares:', error);
            return [];
        }
    }

    obterRadarPorId(id: string): Observable<Radar> {
        return this.http.get<Radar>(`${this.apiUrl}/radares/${id}`);
    }

    buscarRadaresPorRegiao(lat: number, lng: number, raio: number = 5000): Radar[] {
        const radares = this.radaresSubject.value;
        return radares.filter(radar => {
            const distancia = this.calcularDistancia(lat, lng, radar.latitude, radar.longitude);
            return distancia <= raio && radar.ativo;
        });
    }

    getNearbyRadars(lat: number, lng: number, raio: number = 5000): Observable<Radar[]> {
        return this.http.get<Radar[]>(`${this.apiUrl}/radares`).pipe(
            map(radares => radares.filter(radar => {
                const distancia = this.calcularDistancia(lat, lng, radar.latitude, radar.longitude);
                return distancia <= raio && radar.ativo;
            })),
            catchError(error => {
                console.error('Erro ao buscar radares próximos:', error);
                return of([]);
            })
        );
    }

    getAllRadars(): Observable<Radar[]> {
        return this.http.get<Radar[]>(`${this.apiUrl}/radares`).pipe(
            catchError(error => {
                console.error('Erro ao buscar todos os radares:', error);
                return of([]);
            })
        );
    }

    private calcularDistancia(lat1: number, lng1: number, lat2: number, lng2: number): number {
        const R = 6371000; // Raio da Terra em metros
        const dLat = this.toRadians(lat2 - lat1);
        const dLng = this.toRadians(lng2 - lng1);
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
            Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    private toRadians(degrees: number): number {
        return degrees * (Math.PI / 180);
    }
}

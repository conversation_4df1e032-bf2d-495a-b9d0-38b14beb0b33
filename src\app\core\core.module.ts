import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';

// Services
import { GeolocationService, RadarService, AlertService } from './services';

// Guards
import { LocationGuard } from './guards/location.guard';

// Interceptors
import { HttpErrorInterceptor } from './interceptors/http-error.interceptor';

@NgModule({
    declarations: [],
    imports: [
        CommonModule
    ],
    providers: [
        // Services
        GeolocationService,
        RadarService,
        AlertService,

        // Guards
        LocationGuard,

        // Interceptors
        {
            provide: HTTP_INTERCEPTORS,
            useClass: HttpErrorInterceptor,
            multi: true
        }
    ]
})
export class CoreModule { }

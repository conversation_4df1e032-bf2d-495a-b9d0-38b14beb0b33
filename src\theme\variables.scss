// RadarBrasil Theme Variables

// Color palette as specified in README
:root {
    // Primary colors
    --ion-color-primary: #2196F3;
    --ion-color-primary-rgb: 33, 150, 243;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb: 255, 255, 255;
    --ion-color-primary-shade: #1e87db;
    --ion-color-primary-tint: #37a1f4;

    // Secondary colors (Orange for alerts)
    --ion-color-secondary: #FF5722;
    --ion-color-secondary-rgb: 255, 87, 34;
    --ion-color-secondary-contrast: #ffffff;
    --ion-color-secondary-contrast-rgb: 255, 255, 255;
    --ion-color-secondary-shade: #e04e1e;
    --ion-color-secondary-tint: #ff6838;

    // Success colors
    --ion-color-success: #4CAF50;
    --ion-color-success-rgb: 76, 175, 80;
    --ion-color-success-contrast: #ffffff;
    --ion-color-success-contrast-rgb: 255, 255, 255;
    --ion-color-success-shade: #439a46;
    --ion-color-success-tint: #5eb962;

    // Danger colors
    --ion-color-danger: #F44336;
    --ion-color-danger-rgb: 244, 67, 54;
    --ion-color-danger-contrast: #ffffff;
    --ion-color-danger-contrast-rgb: 255, 255, 255;
    --ion-color-danger-shade: #d73a30;
    --ion-color-danger-tint: #f5554a;

    // Additional colors for dark theme support
    --ion-color-medium: #92949c;
    --ion-color-medium-rgb: 146, 148, 156;
    --ion-color-medium-contrast: #ffffff;
    --ion-color-medium-contrast-rgb: 255, 255, 255;
    --ion-color-medium-shade: #808289;
    --ion-color-medium-tint: #9d9fa6;

    --ion-color-light: #f4f5f8;
    --ion-color-light-rgb: 244, 245, 248;
    --ion-color-light-contrast: #000000;
    --ion-color-light-contrast-rgb: 0, 0, 0;
    --ion-color-light-shade: #d7d8da;
    --ion-color-light-tint: #f5f6f9;
}

// Dark theme
@media (prefers-color-scheme: dark) {
    :root {
        --ion-background-color: #1a1a1a;
        --ion-background-color-rgb: 26, 26, 26;

        --ion-text-color: #ffffff;
        --ion-text-color-rgb: 255, 255, 255;

        --ion-card-background: #2d2d2d;
    }
}

// Custom variables for RadarBrasil
:root {
    --radar-speed-color: #00e676;
    --radar-warning-color: #ff9800;
    --radar-danger-color: #f44336;
    --map-overlay-background: rgba(0, 0, 0, 0.8);
    --alert-background: rgba(255, 87, 34, 0.9);
}

// High contrast mode for driving
.high-contrast {
    --ion-text-color: #ffffff;
    --ion-background-color: #000000;
    --ion-color-primary: #00e676;
    --ion-color-secondary: #ff1744;
}

# 🚀 Guia de Instalação - RadarBrasil

## 📋 Pré-requisitos

Antes de começar, certifique-se de ter instalado em sua máquina:

- **Node.js** 18+ ([Download](https://nodejs.org/))
- **NPM** 9+ (vem com Node.js)
- **Android Studio** ([Download](https://developer.android.com/studio))
- **JDK 11** ou superior
- **Git** ([Download](https://git-scm.com/))

## 🛠️ Instalação do Ambiente

### 1. Instalar Ferramentas Globais

```bash
# Instalar Angular CLI
npm install -g @angular/cli@17

# Instalar Ionic CLI
npm install -g @ionic/cli@7

# Verificar instalações
ng version
ionic --version
```

### 2. Configurar Android SDK

1. Abra o Android Studio
2. Vá em **Tools** > **SDK Manager**
3. Instale o **Android API Level 33** (ou superior)
4. Configure as variáveis de ambiente:

```bash
# Windows (PowerShell)
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\tools;$env:ANDROID_HOME\platform-tools"

# Linux/Mac
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

## 📱 Configuração do Projeto

### 1. Clonar e Instalar Dependências

```bash
# Navegar para o diretório do projeto
cd RadarBrasil

# Instalar dependências
npm install

# Verificar se tudo está funcionando
ionic serve
```

### 2. Configurar a API Fake

Em um terminal separado, execute:

```bash
# Instalar JSON Server globalmente
npm install -g json-server

# Executar a API fake
json-server --watch db.json --port 3000
```

A API estará disponível em: `http://localhost:3000`

### Endpoints disponíveis

- `GET /radares` - Lista todos os radares
- `GET /radares/:id` - Busca radar por ID
- `POST /radares` - Cria novo radar
- `PUT /radares/:id` - Atualiza radar
- `DELETE /radares/:id` - Remove radar

## 🏃‍♂️ Executando o Projeto

### Desenvolvimento Web

```bash
# Executar no navegador
ionic serve

# Executar com livereload
ionic serve --lab
```

Acesse: `http://localhost:8100`

### Desenvolvimento Mobile

#### 1. Preparar para Android

```bash
# Adicionar plataforma Android
ionic capacitor add android

# Build da aplicação
ionic build

# Sincronizar com Capacitor
ionic capacitor sync android

# Abrir no Android Studio
ionic capacitor open android
```

#### 2. Executar no Dispositivo/Emulador

```bash
# Executar diretamente (precisa de device conectado)
ionic capacitor run android

# Executar com livereload
ionic capacitor run android --livereload --external
```

## 🔧 Comandos Úteis

### Build e Deploy

```bash
# Build para produção
ionic build --prod

# Sincronizar arquivos
ionic capacitor sync

# Executar no Android
ionic capacitor run android

# Abrir Android Studio
ionic capacitor open android
```

### Desenvolvimento

```bash
# Gerar novo componente
ionic generate component components/meu-componente

# Gerar novo serviço
ionic generate service services/meu-servico

# Gerar nova página
ionic generate page pages/minha-pagina

# Executar testes
npm run test

# Executar lint
npm run lint
```

### Debug e Logs

```bash
# Ver logs do dispositivo Android
adb logcat

# Debug no Chrome DevTools
# Acesse: chrome://inspect/#devices
```

## 📱 Preparando APK para Distribuição

### 1. Build de Release

No Android Studio:

1. **Build** > **Generate Signed Bundle/APK**
2. Selecione **APK**
3. Crie ou selecione uma keystore
4. Escolha **release** como build variant
5. Clique em **Finish**

### 2. Ou via Linha de Comando

```bash
# Navegar para pasta android
cd android

# Build release
./gradlew assembleRelease

# APK estará em: android/app/build/outputs/apk/release/
```

## 🔍 Troubleshooting

### Problemas Comuns

#### 1. Erro de Permissões Android

```bash
# Verificar dispositivos conectados
adb devices

# Reiniciar ADB
adb kill-server
adb start-server
```

#### 2. Erro de Capacitor

```bash
# Limpar e reinstalar
npm run clean
npx cap clean android
npx cap add android
npx cap sync android
```

#### 3. Erro de Dependências

```bash
# Limpar cache npm
npm cache clean --force

# Remover node_modules e reinstalar
rm -rf node_modules
npm install
```

#### 4. Problemas de CORS na API

Se estiver usando a API fake e tiver problemas de CORS:

```bash
# Executar JSON Server com CORS habilitado
json-server --watch db.json --port 3000 --middlewares cors.js
```

Crie o arquivo `cors.js`:

```javascript
module.exports = (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Headers', '*')
  res.header('Access-Control-Allow-Methods', '*')
  next()
}
```

## 📚 Recursos Adicionais

### Documentação

- [Ionic Documentation](https://ionicframework.com/docs)
- [Angular Documentation](https://angular.io/docs)
- [Capacitor Documentation](https://capacitorjs.com/docs)

### Ferramentas Úteis

- **Ionic DevApp** - Teste no dispositivo sem build
- **Chrome DevTools** - Debug da aplicação
- **Android Studio Profiler** - Análise de performance
- **Ionic CLI** - Comandos e helpers

## 🆘 Suporte

Se encontrar problemas durante a instalação:

1. Verifique se todas as dependências estão instaladas
2. Consulte os logs de erro
3. Verifique a documentação oficial
4. Procure por issues similares no GitHub

---

**Próximos Passos:** Após a instalação, consulte o README.md para entender a arquitetura e funcionalidades do aplicativo.

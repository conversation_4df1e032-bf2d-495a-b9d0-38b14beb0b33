import { Routes } from '@angular/router';
import { LocationGuard } from './core/guards/location.guard';

export const routes: Routes = [
    {
        path: 'home',
        loadComponent: () => import('./pages/home/<USER>').then((m) => m.HomePage),
        canActivate: [LocationGuard]
    },
    {
        path: 'radar-list',
        loadComponent: () => import('./pages/radar-list/radar-list.page').then((m) => m.RadarListPage),
        canActivate: [LocationGuard]
    },
    {
        path: 'settings',
        loadComponent: () => import('./pages/settings/settings.page').then((m) => m.SettingsPage)
    },
    {
        path: '',
        redirectTo: 'home',
        pathMatch: 'full',
    },
];

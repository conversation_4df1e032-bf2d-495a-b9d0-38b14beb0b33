export interface Radar {
    id: string;
    latitude: number;
    longitude: number;
    tipo: 'fixo' | 'movel' | 'semaforo';
    velocidadeMaxima: number;
    direcao?: 'norte' | 'sul' | 'leste' | 'oeste';
    ativo: boolean;
    endereco?: string;
    dataAtualizacao: Date;
}

export type TipoRadar = 'fixo' | 'movel' | 'semaforo';
export type DirecaoRadar = 'norte' | 'sul' | 'leste' | 'oeste';

export interface Localizacao {
    latitude: number;
    longitude: number;
    velocidade: number;
    direcao: number;
    precisao: number;
    timestamp: Date;
}

export interface Alerta {
    id: string;
    tipo: TipoAlerta;
    mensagem: string;
    radar?: Radar;
    distancia?: number;
    ativo: boolean;
    timestamp?: Date;
}

export type TipoAlerta = 'radar' | 'velocidade' | 'geral';

export interface ConfiguracaoApp {
    distanciaAlerta: number; // em metros
    alertaSonoro: boolean;
    modoNoturno: 'auto' | 'sempre' | 'nunca';
    unidadeVelocidade: 'kmh' | 'mph';
    volumeAlertas: number; // 0-100
    vibracaoAlertas: boolean;
}

export interface LimiteVelocidade {
    tipo_via: string;
    velocidade_maxima: number;
    descricao: string;
}

export interface EstadoNavegacao {
    velocidadeAtual: number;
    limiteVelocidadeAtual: number;
    direcaoAtual: number;
    localizacaoAtual: Localizacao;
    radaresProximos: RadarProximo[];
    alertaAtivo?: Alerta;
}

export interface RadarProximo extends Radar {
    distancia: number;
    tempoEstimado: number; // em segundos
    dentroDoRaio: boolean;
}

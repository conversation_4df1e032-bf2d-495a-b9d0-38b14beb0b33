import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Capacitor } from '@capacitor/core';
import { GeolocationService } from '../services';

@Injectable({
  providedIn: 'root'
})
export class LocationGuard implements CanActivate {

  constructor(
    private geolocationService: GeolocationService,
    private router: Router
  ) { }

  async canActivate(): Promise<boolean> {
    try {
      const hasPermission = await this.geolocationService.requestPermissions();

      if (!hasPermission) {
        // Em ambiente web de desenvolvimento, permita acesso mesmo sem geolocalização
        if (!Capacitor.isNativePlatform()) {
          console.warn('Geolocalização não disponível no ambiente web. Permitindo acesso para desenvolvimento.');
          return true;
        }

        // Em dispositivo móvel, redirecionar para configurações
        this.router.navigate(['/settings']);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao verificar permissões de localização:', error);

      // Em caso de erro no ambiente web, permitir acesso
      if (!Capacitor.isNativePlatform()) {
        console.warn('Erro na verificação de geolocalização no web. Permitindo acesso para desenvolvimento.');
        return true;
      }

      // Em dispositivo móvel, bloquear acesso em caso de erro
      this.router.navigate(['/settings']);
      return false;
    }
  }
}

export interface GeolocationConfig {
    /** Timeout para obtenção de localização com alta precisão (ms) */
    highAccuracyTimeout: number;

    /** Timeout para obtenção de localização com baixa precisão (ms) */
    lowAccuracyTimeout: number;

    /** Timeout para verificação de disponibilidade (ms) */
    availabilityCheckTimeout: number;

    /** Timeout para monitoramento watchPosition (ms) */
    watchTimeout: number;

    /** Idade máxima do cache para alta precisão (ms) */
    highAccuracyMaxAge: number;

    /** Idade máxima do cache para baixa precisão (ms) */
    lowAccuracyMaxAge: number;

    /** Idade máxima do cache para último recurso (ms) */
    fallbackMaxAge: number;

    /** Número máximo de tentativas para obter localização */
    maxRetries: number;

    /** Delay base entre tentativas (ms) */
    retryDelay: number;

    /** Idade máxima de localização considerada "recente" (ms) */
    recentLocationMaxAge: number;
}

export const DEFAULT_GEOLOCATION_CONFIG: GeolocationConfig = {
    // Timeouts mais generosos para evitar falhas
    highAccuracyTimeout: 25000,    // 25 segundos para alta precisão
    lowAccuracyTimeout: 15000,     // 15 segundos para baixa precisão
    availabilityCheckTimeout: 10000, // 10 segundos para verificação
    watchTimeout: 45000,           // 45 segundos para monitoramento

    // Configurações de cache
    highAccuracyMaxAge: 60000,     // 1 minuto para alta precisão
    lowAccuracyMaxAge: 120000,     // 2 minutos para baixa precisão
    fallbackMaxAge: 600000,        // 10 minutos para último recurso

    // Configurações de retry
    maxRetries: 3,                 // 3 tentativas máximas
    retryDelay: 2000,              // 2 segundos base entre tentativas
    recentLocationMaxAge: 120000,  // 2 minutos considera localização recente
};

/**
 * Configuração otimizada para ambientes com GPS fraco ou indoor
 */
export const INDOOR_GEOLOCATION_CONFIG: GeolocationConfig = {
    ...DEFAULT_GEOLOCATION_CONFIG,
    highAccuracyTimeout: 35000,    // Mais tempo para indoor
    lowAccuracyTimeout: 20000,
    watchTimeout: 60000,
    maxRetries: 5,                 // Mais tentativas para indoor
    lowAccuracyMaxAge: 300000,     // 5 minutos de cache para indoor
    fallbackMaxAge: 900000,        // 15 minutos para último recurso
    recentLocationMaxAge: 300000,  // 5 minutos considera localização recente
};

/**
 * Configuração otimizada para rápida obtenção de localização
 */
export const FAST_GEOLOCATION_CONFIG: GeolocationConfig = {
    ...DEFAULT_GEOLOCATION_CONFIG,
    highAccuracyTimeout: 15000,    // Timeouts menores para velocidade
    lowAccuracyTimeout: 10000,
    watchTimeout: 30000,
    maxRetries: 2,                 // Menos tentativas para velocidade
    recentLocationMaxAge: 60000,   // 1 minuto considera localização recente
};
